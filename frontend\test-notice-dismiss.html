<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告弹窗持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 公告弹窗持久化功能测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此测试页面用于验证公告弹窗的"下次不再提示"功能是否正常工作。</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>检查当前localStorage中的设置状态</li>
                <li>模拟设置"下次不再提示"</li>
                <li>模拟清除设置（重新显示）</li>
                <li>验证设置是否持久化</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 当前状态检查</h3>
            <button onclick="checkCurrentStatus()">检查当前状态</button>
            <div id="currentStatus" class="status info">
                点击"检查当前状态"按钮查看当前设置
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 功能测试</h3>
            <button onclick="setDismissed()">模拟点击"下次不再提示"</button>
            <button onclick="clearDismissed()">清除设置（重新显示公告）</button>
            <button onclick="simulatePageLoad()">模拟页面加载检查</button>
            <button onclick="realTimeVerify()" style="background: #28a745;">🔄 实时验证清除效果</button>
            <div id="testResult" class="status info">
                点击按钮进行功能测试
            </div>
        </div>

        <div class="test-section">
            <h3>🚨 问题排查</h3>
            <button onclick="troubleshoot()" style="background: #dc3545;">🔧 一键排查问题</button>
            <button onclick="forceClearAll()" style="background: #e74c3c;">💥 强制清除所有相关设置</button>
            <button onclick="openMainApp()" style="background: #6f42c1;">🚀 打开主应用测试</button>
            <div id="troubleshootResult" class="status info">
                如果清除设置后弹窗仍不显示，点击"一键排查问题"
            </div>
        </div>

        <div class="test-section">
            <h3>📊 localStorage 详情</h3>
            <button onclick="showLocalStorageDetails()">查看localStorage详情</button>
            <div id="localStorageDetails" class="status info">
                点击按钮查看localStorage中的所有相关数据
            </div>
        </div>
    </div>

    <script>
        // 使用与实际应用相同的键名
        const NOTICE_DISMISSED_KEY = 'elevenlabs_notice_dismissed';

        function checkCurrentStatus() {
            const isNoticeDismissed = localStorage.getItem(NOTICE_DISMISSED_KEY) === 'true';
            const statusDiv = document.getElementById('currentStatus');
            
            if (isNoticeDismissed) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ 当前状态：已设置"下次不再提示"<br>公告弹窗将不会显示';
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = '📢 当前状态：未设置"下次不再提示"<br>公告弹窗将正常显示';
            }
        }

        function setDismissed() {
            localStorage.setItem(NOTICE_DISMISSED_KEY, 'true');
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = 'status success';
            resultDiv.innerHTML = '✅ 成功设置"下次不再提示"<br>localStorage已更新';
            checkCurrentStatus();
        }

        function clearDismissed() {
            // 记录清除前的状态
            const beforeValue = localStorage.getItem(NOTICE_DISMISSED_KEY);
            console.log('清除前的值:', beforeValue);

            // 执行清除操作
            localStorage.removeItem(NOTICE_DISMISSED_KEY);

            // 验证清除后的状态
            const afterValue = localStorage.getItem(NOTICE_DISMISSED_KEY);
            console.log('清除后的值:', afterValue);

            const resultDiv = document.getElementById('testResult');
            resultDiv.className = 'status success';
            resultDiv.innerHTML = `
                🔄 <strong>已清除"下次不再提示"设置</strong><br>
                清除前: ${beforeValue || '(未设置)'}<br>
                清除后: ${afterValue || '(未设置)'}<br>
                <strong>现在公告弹窗应该重新显示</strong><br>
                <em>请刷新主应用页面验证效果</em>
            `;
            checkCurrentStatus();
        }

        function simulatePageLoad() {
            const isNoticeDismissed = localStorage.getItem(NOTICE_DISMISSED_KEY) === 'true';
            const resultDiv = document.getElementById('testResult');
            
            if (!isNoticeDismissed) {
                resultDiv.className = 'status info';
                resultDiv.innerHTML = '📢 页面加载模拟：公告弹窗将显示<br>因为用户未选择"下次不再提示"';
            } else {
                resultDiv.className = 'status success';
                resultDiv.innerHTML = '🚫 页面加载模拟：公告弹窗将不显示<br>因为用户已选择"下次不再提示"';
            }
        }

        function showLocalStorageDetails() {
            const detailsDiv = document.getElementById('localStorageDetails');
            let details = '<strong>localStorage 相关数据：</strong><br><br>';
            
            // 显示公告相关的设置
            const noticeValue = localStorage.getItem(NOTICE_DISMISSED_KEY);
            details += `🔑 ${NOTICE_DISMISSED_KEY}: ${noticeValue || '(未设置)'}<br>`;
            
            // 显示其他相关的localStorage项目
            const relevantKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('notice') || key.includes('dismissed') || key.includes('elevenlabs'))) {
                    relevantKeys.push(key);
                }
            }
            
            if (relevantKeys.length > 0) {
                details += '<br><strong>其他相关设置：</strong><br>';
                relevantKeys.forEach(key => {
                    if (key !== NOTICE_DISMISSED_KEY) {
                        details += `🔑 ${key}: ${localStorage.getItem(key)}<br>`;
                    }
                });
            }
            
            detailsDiv.className = 'status info';
            detailsDiv.innerHTML = details;
        }

        function realTimeVerify() {
            const resultDiv = document.getElementById('testResult');
            const currentValue = localStorage.getItem(NOTICE_DISMISSED_KEY);
            const shouldShow = currentValue !== 'true';

            resultDiv.className = shouldShow ? 'status success' : 'status info';
            resultDiv.innerHTML = `
                🔍 <strong>实时验证结果：</strong><br>
                localStorage值: ${currentValue || '(未设置)'}<br>
                弹窗应该显示: ${shouldShow ? '✅ 是' : '❌ 否'}<br>
                <strong>${shouldShow ? '如果主应用中仍看不到弹窗，可能是缓存问题' : '弹窗已被隐藏，这是正常的'}</strong>
            `;
        }

        function troubleshoot() {
            const troubleDiv = document.getElementById('troubleshootResult');
            const currentValue = localStorage.getItem(NOTICE_DISMISSED_KEY);

            let diagnosis = '<strong>🔧 问题诊断结果：</strong><br><br>';

            if (currentValue === 'true') {
                diagnosis += '❌ <strong>问题发现：</strong>localStorage中仍有"不再提示"设置<br>';
                diagnosis += '🔧 <strong>解决方案：</strong>点击"清除设置"按钮<br>';
            } else if (currentValue === null || currentValue === undefined) {
                diagnosis += '✅ <strong>localStorage状态正常：</strong>未设置"不再提示"<br>';
                diagnosis += '🤔 <strong>可能原因：</strong><br>';
                diagnosis += '• 浏览器缓存问题 - 请按Ctrl+F5强制刷新主应用<br>';
                diagnosis += '• 开发服务器未重启 - 请重启开发服务器<br>';
                diagnosis += '• 代码未生效 - 检查文件是否保存<br>';
            } else {
                diagnosis += `⚠️ <strong>异常值：</strong>${currentValue}<br>`;
                diagnosis += '🔧 <strong>建议：</strong>清除设置后重试<br>';
            }

            troubleDiv.className = 'status info';
            troubleDiv.innerHTML = diagnosis;
        }

        function forceClearAll() {
            // 强制清除所有可能相关的localStorage项
            const keysToRemove = [];

            // 遍历所有localStorage键，找到相关的
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.includes('notice') ||
                    key.includes('dismissed') ||
                    key.includes('elevenlabs') ||
                    key === NOTICE_DISMISSED_KEY
                )) {
                    keysToRemove.push(key);
                }
            }

            // 清除找到的键
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log('强制清除:', key);
            });

            const troubleDiv = document.getElementById('troubleshootResult');
            troubleDiv.className = 'status success';
            troubleDiv.innerHTML = `
                💥 <strong>强制清除完成</strong><br>
                清除的键: ${keysToRemove.length > 0 ? keysToRemove.join(', ') : '(无相关键)'}<br>
                <strong>现在请刷新主应用页面测试</strong>
            `;

            // 同时更新状态显示
            checkCurrentStatus();
        }

        function openMainApp() {
            // 获取当前域名和端口，打开主应用
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port;
            const mainAppUrl = `${protocol}//${hostname}${port ? ':' + port : ''}`;

            window.open(mainAppUrl, '_blank');

            const troubleDiv = document.getElementById('troubleshootResult');
            troubleDiv.className = 'status success';
            troubleDiv.innerHTML = `
                🚀 <strong>已尝试打开主应用</strong><br>
                URL: ${mainAppUrl}<br>
                <em>请在新标签页中检查公告弹窗是否显示</em>
            `;
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkCurrentStatus();
        };
    </script>
</body>
</html>
